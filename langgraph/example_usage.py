"""
使用示例：如何在其他文件中导入和使用需求文档分析功能

这个文件展示了如何使用 test_langgraph.py 中封装的分析函数
"""

import asyncio
import json
from datetime import datetime

# 导入封装的分析函数
from test_langgraph import analyze_requirement_document


async def example_async_usage():
    """异步使用示例"""
    print("🚀 异步使用示例")
    print("=" * 50)
    
    # 示例需求文档
    requirement_doc = "ros点云计数"
    
    try:
        # 调用异步分析函数
        result = await analyze_requirement_document(requirement_doc)
        
        # 输出分析结果
        print(f"📋 需求文档: {result['requirement_doc'][:100]}...")
        print(f"🔍 搜索次数: {result['search_count']}")
        print(f"⏰ 分析时间: {result['timestamp']}")
        print(f"🧵 线程ID: {result['thread_id']}")
        print(f"📊 消息数量: {result['messages_count']}")
        print(f"🤖 模型响应数量: {result['total_model_responses']}")
        print(f"✅ 分析完成: {result['is_complete']}")
        
        print("\n📝 最终分析结果:")
        print("=" * 50)
        print(result['final_understanding'])
        
        # 保存结果到文件（可选）
        output_file = f"example_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 结果已保存到: {output_file}")
        
        return result
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None



async def main():
    """主函数：运行所有示例"""
    print("🎯 需求文档分析功能使用示例")
    print("=" * 60)
    
    # 1. 异步使用示例
    await example_async_usage()
    
    
    print("\n🎉 所有示例运行完成！")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
