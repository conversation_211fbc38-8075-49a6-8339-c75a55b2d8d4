import json
import os
import logging
import click
import contextlib
import uvicorn
from typing import Any, Dict, List
from openai import OpenAI
from pymilvus import MilvusClient
from collections.abc import AsyncIterator
import mcp.types as types
from mcp.server.lowlevel import Server
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from starlette.applications import Starlette
from starlette.routing import Mount

# OpenAI配置
client = OpenAI(
    api_key="",  # 中台APIkey
    base_url="",  # 调用地址，根据你的vLLM服务设置
)

# 连接到Milvus服务器
milvus_client = MilvusClient(uri="", token="root:Milvus", db_name="codebase")

# 集合名称
collection_name = "drivers_master"

# 确保集合已加载到内存
try:
    milvus_client.load_collection(collection_name=collection_name)
    print(f"成功加载Milvus集合: {collection_name}")
except Exception as e:
    print(f"加载Milvus集合失败: {e}")


async def text_to_vector(text: str) -> List[float]:
    """将文本转换为向量表示"""
    try:
        response = client.embeddings.create(
            model="Qwen3-Embedding-4B",
            input=text,
            encoding_format="float",
        )
        vector = response.data[0].embedding
        print(f"成功将文本转换为向量，维度: {len(vector)}")
        return vector
    except Exception as e:
        print(f"向量转换失败: {e}")
        raise e


def search_by_vector(input_vector: List[float], limit: int = 5) -> List[Dict[str, Any]]:
    """使用向量在Milvus中搜索相似内容"""
    try:
        search_params = {
            "metric_type": "COSINE",
            "params": {}
        }

        search_result = milvus_client.search(
            collection_name=collection_name,
            data=[input_vector],
            limit=limit,
            output_fields=["*"],
            search_params=search_params
        )

        # 处理搜索结果，提取有用信息
        processed_results = []
        if search_result:
            for i, results in enumerate(search_result):
                for hit in results:
                    result_item = {
                        "id": hit["id"],
                        "similarity": hit["distance"],
                        "metadata": {}
                    }

                    # 添加所有元数据
                    if "entity" in hit:
                        for key, value in hit["entity"].items():
                            if key != "vector":  # 排除向量字段
                                if key == "raw_code":
                                    result_item["code"] = value
                                else:
                                    result_item["metadata"][key] = value

                    processed_results.append(result_item)

        return processed_results
    except Exception as e:
        print(f"向量搜索失败: {e}")
        raise e


def format_search_results(results: List[Dict[str, Any]]) -> str:
    """将搜索结果格式化为易读的文本"""
    if not results:
        return "未找到相关代码。"

    formatted_text = "找到以下相关代码：\n"
    for i, result in enumerate(results):
        formatted_text += f"结果 {i + 1}:\n"
        formatted_text += f"相似度: {result['similarity']:.4f}\n"

        # 添加元数据信息
        if result.get("metadata"):
            meta = result["metadata"]
            if "chunk_id" in meta:
                formatted_text += f"块ID: {meta.get('chunk_id')}\n"
            if "file_path" in meta:
                formatted_text += f"文件路径: {meta.get('file_path')}\n"
            if "start_line" in meta and "end_line" in meta:
                formatted_text += f"行范围: {meta.get('start_line')}-{meta.get('end_line')}\n"

        # 添加完整代码内容（移除10行限制）
        if "code" in result:
            code = result["code"]
            formatted_text += f"代码:\n```\n{code}\n```\n"

        formatted_text += "-" * 50 + "\n"

    return formatted_text


# 通过click设置命令行启动参数
@click.command()
@click.option("--port", default=3001, help="Port to listen on for HTTP")
@click.option(
    "--log-level",
    default="INFO",
    help="日志级别(DEBUG, INFO, WARNING, ERROR, CRITICAL)",
)
@click.option(
    "--json-response",
    is_flag=True,
    default=False,
    help="使用JSON响应代替SSE 流式输出",
)
def main(port, log_level, json_response):
    # ---------------------- 设置日志 ----------------------
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger("codebase-search-server")

    # 创建MCP服务端
    app = Server("Codebase-Search-Streamable-HTTP-MCP-Server")

    # 工具调用
    @app.call_tool()
    async def call_tool(name, arguments):
        """
        Handle the 'search_codebase' tool call.
        """
        ctx = app.request_context
        query = arguments.get("query")
        limit = arguments.get("limit", 5)

        if not query:
            raise ValueError("'query' is required in arguments")

        # 准备发起搜索请求发送日志
        await ctx.session.send_log_message(
            level="info",
            data=f"Searching codebase for: {query}",
            logger="codebase-search",
            related_request_id=ctx.request_id,
        )

        try:
            # 转换查询为向量
            query_vector = await text_to_vector(query)

            # 搜索相似向量
            search_results = search_by_vector(query_vector, limit)

            # 格式化结果
            formatted_results = format_search_results(search_results)

        except Exception as err:
            # 搜索请求失败发送日志
            await ctx.session.send_log_message(
                level="error",
                data=str(err),
                logger="codebase-search",
                related_request_id=ctx.request_id,
            )
            raise

        # 搜索请求成功发送日志
        await ctx.session.send_log_message(
            level="info",
            data="Codebase search completed successfully!",
            logger="codebase-search",
            related_request_id=ctx.request_id,
        )

        return [
            types.TextContent(type="text", text=formatted_results)
        ]

    # 工具列表
    @app.list_tools()
    async def list_tools():
        """
        Expose available tools to the LLM.
        """
        return [
            types.Tool(
                name="search_codebase",
                description="使用自然语言查询代码库，返回最相关的代码片段",
                inputSchema={
                    "type": "object",
                    "required": ["query"],
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "自然语言查询",
                        },
                        "limit": {
                            "type": "integer",
                            "description": "返回结果数量，默认为5",
                            "default": 5
                        }
                    },
                },
            )
        ]

    # ----------管理请求会话--------------
    session_manager = StreamableHTTPSessionManager(
        app=app,
        event_store=None,  # 无状态，不保存历史事件
        json_response=json_response,
        stateless=True
    )

    async def handle_streamable_http(scope, receive, send):
        await session_manager.handle_request(scope, receive, send)

    @contextlib.asynccontextmanager
    async def lifespan(app):
        async with session_manager.run():
            logger.info("Codebase Search MCP server started!")
            try:
                yield
            finally:
                logger.info("Codebase Search MCP server shutting down…")

    # 将MCP服务挂载到/mcp路径上，用户访问整个路径时，就会进入刚才创建的MCP HTTP会话管理器
    starlette_app = Starlette(
        debug=False,
        routes=[Mount("/mcp", app=handle_streamable_http)],
        lifespan=lifespan,
    )

    # 利用uvicorn启动ASGI服务器并监听指定端口
    uvicorn.run(starlette_app, host="0.0.0.0", port=port)

    return 0


if __name__ == "__main__":
    main()
